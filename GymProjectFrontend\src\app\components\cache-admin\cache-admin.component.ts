import { Component, OnInit, OnDestroy } from '@angular/core';
import { CacheAdminService, CacheStatistics, CacheHealth, CacheKeysResponse, CacheWarmupRequest, TenantCacheDetails } from '../../services/cache-admin.service';
import { ToastrService } from 'ngx-toastr';
import { interval, Subscription } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Component({
  selector: 'app-cache-admin',
  templateUrl: './cache-admin.component.html',
  styleUrls: ['./cache-admin.component.css'],
  standalone:false
})
export class CacheAdminComponent implements OnInit, OnDestroy {
  
  // Loading States
  isLoading = true;
  isStatisticsLoading = false;
  isHealthLoading = false;
  isKeysLoading = false;
  isOperationLoading = false;

  // Data Properties
  statistics: CacheStatistics | null = null;
  health: CacheHealth | null = null;
  cacheKeys: CacheKeysResponse | null = null;
  tenantDetails: TenantCacheDetails | null = null;

  // UI State
  activeTab = 'overview';
  autoRefreshEnabled = true;
  refreshInterval = 30; // seconds
  
  // Pagination
  currentPage = 1;
  pageSize = 20;
  
  // Search & Filter
  searchPattern = '';
  selectedEntity = '';
  
  // Auto-refresh subscription
  private refreshSubscription?: Subscription;
  
  // Available entities for filtering
  availableEntities = [
    { key: '', label: 'Tüm Entityler' },
    { key: 'member', label: 'Üyeler' },
    { key: 'payment', label: 'Ödemeler' },
    { key: 'membership', label: 'Üyelikler' },
    { key: 'user', label: 'Kullanıcılar' },
    { key: 'company', label: 'Şirket' },
    { key: 'product', label: 'Ürünler' },
    { key: 'transaction', label: 'İşlemler' }
  ];

  constructor(
    private cacheAdminService: CacheAdminService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.loadInitialData();
    this.startAutoRefresh();
  }

  ngOnDestroy(): void {
    this.stopAutoRefresh();
  }

  /**
   * Initial data loading
   */
  async loadInitialData(): Promise<void> {
    this.isLoading = true;
    
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadHealth(),
        this.loadCacheKeys(),
        this.loadTenantDetails()
      ]);
    } catch (error) {
      console.error('Initial data loading error:', error);
      this.toastr.error('Cache verileri yüklenirken hata oluştu', 'Hata');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Load cache statistics
   */
  async loadStatistics(): Promise<void> {
    this.isStatisticsLoading = true;
    
    try {
      const response = await this.cacheAdminService.getStatistics().toPromise();
      if (response?.success && response.data) {
        this.statistics = response.data;
      }
    } catch (error) {
      console.error('Statistics loading error:', error);
      if (!this.isLoading) {
        this.toastr.error('Cache istatistikleri yüklenemedi', 'Hata');
      }
    } finally {
      this.isStatisticsLoading = false;
    }
  }

  /**
   * Load cache health info
   */
  async loadHealth(): Promise<void> {
    this.isHealthLoading = true;
    
    try {
      const response = await this.cacheAdminService.getHealthInfo().toPromise();
      if (response?.success && response.data) {
        this.health = response.data;
      }
    } catch (error) {
      console.error('Health loading error:', error);
      if (!this.isLoading) {
        this.toastr.error('Cache sağlık durumu yüklenemedi', 'Hata');
      }
    } finally {
      this.isHealthLoading = false;
    }
  }

  /**
   * Load cache keys with pagination
   */
  async loadCacheKeys(): Promise<void> {
    this.isKeysLoading = true;
    
    try {
      const response = await this.cacheAdminService.getCompanyCacheKeys(this.currentPage, this.pageSize).toPromise();
      if (response?.success && response.data) {
        this.cacheKeys = response.data;
      }
    } catch (error) {
      console.error('Cache keys loading error:', error);
      if (!this.isLoading) {
        this.toastr.error('Cache key\'leri yüklenemedi', 'Hata');
      }
    } finally {
      this.isKeysLoading = false;
    }
  }

  /**
   * Load tenant cache details
   */
  async loadTenantDetails(): Promise<void> {
    try {
      const response = await this.cacheAdminService.getTenantCacheDetails().toPromise();
      if (response?.success && response.data) {
        this.tenantDetails = response.data;
      }
    } catch (error) {
      console.error('Tenant details loading error:', error);
    }
  }

  // Tab Management
  setActiveTab(tab: string): void {
    this.activeTab = tab;
    if (tab === 'keys' && !this.cacheKeys) {
      this.loadCacheKeys();
    }
  }

  // Auto-refresh Management
  startAutoRefresh(): void {
    if (this.autoRefreshEnabled && !this.refreshSubscription) {
      this.refreshSubscription = interval(this.refreshInterval * 1000)
        .pipe(
          switchMap(() => {
            if (this.activeTab === 'overview') {
              return Promise.all([this.loadStatistics(), this.loadHealth()]);
            } else if (this.activeTab === 'keys') {
              return this.loadCacheKeys();
            }
            return of(null);
          }),
          catchError(error => {
            console.error('Auto-refresh error:', error);
            return of(null);
          })
        )
        .subscribe();
    }
  }

  stopAutoRefresh(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
      this.refreshSubscription = undefined;
    }
  }

  toggleAutoRefresh(): void {
    this.autoRefreshEnabled = !this.autoRefreshEnabled;
    if (this.autoRefreshEnabled) {
      this.startAutoRefresh();
    } else {
      this.stopAutoRefresh();
    }
  }

  // Manual refresh
  async refreshData(): Promise<void> {
    if (this.activeTab === 'overview') {
      await Promise.all([this.loadStatistics(), this.loadHealth()]);
    } else if (this.activeTab === 'keys') {
      await this.loadCacheKeys();
    }
    this.toastr.success('Veriler güncellendi', 'Başarılı');
  }

  // Pagination
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadCacheKeys();
  }

  // Utility methods
  getHealthStatusClass(): string {
    if (!this.health) return 'text-muted';
    return this.health.isConnected ? 'text-success' : 'text-danger';
  }

  getHealthStatusIcon(): string {
    if (!this.health) return 'fas fa-question-circle';
    return this.health.isConnected ? 'fas fa-check-circle' : 'fas fa-times-circle';
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(ms: number): string {
    if (ms < 1000) return `${ms.toFixed(2)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  }

  // Cache Operations
  async clearTenantCache(): Promise<void> {
    if (!confirm('Şirketinizin tüm cache verilerini silmek istediğinizden emin misiniz?')) {
      return;
    }

    this.isOperationLoading = true;
    try {
      const response = await this.cacheAdminService.clearTenantCache().toPromise();
      if (response?.success) {
        this.toastr.success(`${response.data?.removedCount || 0} adet cache silindi`, 'Başarılı');
        await this.refreshData();
      } else {
        this.toastr.error('Cache temizleme işlemi başarısız', 'Hata');
      }
    } catch (error) {
      console.error('Clear cache error:', error);
      this.toastr.error('Cache temizlenirken hata oluştu', 'Hata');
    } finally {
      this.isOperationLoading = false;
    }
  }

  async clearCacheByPattern(pattern: string): Promise<void> {
    if (!confirm(`"${pattern}" pattern'ine uygun cache'leri silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.isOperationLoading = true;
    try {
      const response = await this.cacheAdminService.clearCacheByPattern(pattern).toPromise();
      if (response?.success) {
        this.toastr.success(`${response.data?.removedCount || 0} adet cache silindi`, 'Başarılı');
        await this.refreshData();
      } else {
        this.toastr.error('Pattern cache temizleme işlemi başarısız', 'Hata');
      }
    } catch (error) {
      console.error('Clear pattern cache error:', error);
      this.toastr.error('Pattern cache temizlenirken hata oluştu', 'Hata');
    } finally {
      this.isOperationLoading = false;
    }
  }

  async warmupCache(): Promise<void> {
    const warmupRequest: CacheWarmupRequest = {
      warmupMembers: true,
      warmupPayments: true,
      warmupMemberships: true,
      warmupUsers: false,
      warmupCompanySettings: false
    };

    this.isOperationLoading = true;
    try {
      const response = await this.cacheAdminService.warmupCache(warmupRequest).toPromise();
      if (response?.success) {
        this.toastr.success(`Cache warmup tamamlandı (${response.data?.totalDuration}ms)`, 'Başarılı');
        await this.refreshData();
      } else {
        this.toastr.error('Cache warmup işlemi başarısız', 'Hata');
      }
    } catch (error) {
      console.error('Cache warmup error:', error);
      this.toastr.error('Cache warmup sırasında hata oluştu', 'Hata');
    } finally {
      this.isOperationLoading = false;
    }
  }

  async deleteCacheKey(key: string): Promise<void> {
    if (!confirm(`"${key}" cache key'ini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    try {
      const response = await this.cacheAdminService.deleteCacheKey(key).toPromise();
      if (response?.success && response.data?.removed) {
        this.toastr.success('Cache key başarıyla silindi', 'Başarılı');
        await this.loadCacheKeys();
      } else {
        this.toastr.error('Cache key silinemedi', 'Hata');
      }
    } catch (error) {
      console.error('Delete cache key error:', error);
      this.toastr.error('Cache key silinirken hata oluştu', 'Hata');
    }
  }

  // Search and Filter
  onSearchPatternChange(): void {
    // Debounce search
    setTimeout(() => {
      this.currentPage = 1;
      this.loadCacheKeys();
    }, 500);
  }

  onEntityFilterChange(): void {
    this.currentPage = 1;
    this.loadCacheKeys();
  }

  // Entity Statistics Helpers
  getEntityKeys(): Array<{entity: string, count: number}> {
    if (!this.statistics?.keysByEntity) return [];

    return Object.entries(this.statistics.keysByEntity)
      .map(([entity, count]) => ({ entity, count }))
      .sort((a, b) => b.count - a.count);
  }

  getEntityLabel(entity: string): string {
    const found = this.availableEntities.find(e => e.key === entity);
    return found ? found.label : entity;
  }

  // Performance Metrics
  getPerformanceStatus(): 'excellent' | 'good' | 'warning' | 'critical' {
    if (!this.health) return 'critical';

    const pingTime = this.health.pingTime;
    if (pingTime < 10) return 'excellent';
    if (pingTime < 50) return 'good';
    if (pingTime < 100) return 'warning';
    return 'critical';
  }

  getPerformanceClass(): string {
    const status = this.getPerformanceStatus();
    switch (status) {
      case 'excellent': return 'text-success';
      case 'good': return 'text-info';
      case 'warning': return 'text-warning';
      case 'critical': return 'text-danger';
      default: return 'text-muted';
    }
  }

  // Memory Usage Helpers
  getMemoryUsagePercentage(): number {
    if (!this.health?.serverInfo?.maxMemory || !this.health?.serverInfo?.usedMemory) {
      return 0;
    }

    const used = parseInt(this.health.serverInfo.usedMemory);
    const max = parseInt(this.health.serverInfo.maxMemory);

    return Math.round((used / max) * 100);
  }

  getMemoryUsageClass(): string {
    const percentage = this.getMemoryUsagePercentage();
    if (percentage < 60) return 'bg-success';
    if (percentage < 80) return 'bg-warning';
    return 'bg-danger';
  }

  // Additional UI Properties
  clearPattern = '';

  // Additional Methods for HTML Template
  formatTTL(ttl: number): string {
    if (ttl <= 0) return 'Kalıcı';

    const hours = Math.floor(ttl / 3600);
    const minutes = Math.floor((ttl % 3600) / 60);
    const seconds = ttl % 60;

    if (hours > 0) {
      return `${hours}s ${minutes}d ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}d ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      this.toastr.success('Panoya kopyalandı', 'Başarılı');
    }).catch(() => {
      this.toastr.error('Kopyalama başarısız', 'Hata');
    });
  }

  async viewCacheKeyValue(key: string): Promise<void> {
    try {
      const response = await this.cacheAdminService.getCacheKeyValue(key).toPromise();
      if (response?.success && response.data) {
        // Modal veya dialog ile değeri göster
        alert(`Key: ${key}\n\nValue: ${JSON.stringify(response.data.value, null, 2)}`);
      } else {
        this.toastr.error('Cache key değeri alınamadı', 'Hata');
      }
    } catch (error) {
      console.error('View cache key value error:', error);
      this.toastr.error('Cache key değeri görüntülenirken hata oluştu', 'Hata');
    }
  }

  getPaginationPages(): number[] {
    if (!this.cacheKeys?.pagination) return [];

    const totalPages = this.cacheKeys.pagination.totalPages;
    const currentPage = this.currentPage;
    const pages: number[] = [];

    // Simple pagination logic - show 5 pages around current
    const start = Math.max(1, currentPage - 2);
    const end = Math.min(totalPages, currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  async testConnection(): Promise<void> {
    this.isOperationLoading = true;
    try {
      await this.loadHealth();
      if (this.health?.isConnected) {
        this.toastr.success(`Bağlantı başarılı! Ping: ${this.formatDuration(this.health.pingTime)}`, 'Başarılı');
      } else {
        this.toastr.error('Redis bağlantısı başarısız', 'Hata');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      this.toastr.error('Bağlantı testi sırasında hata oluştu', 'Hata');
    } finally {
      this.isOperationLoading = false;
    }
  }
}
