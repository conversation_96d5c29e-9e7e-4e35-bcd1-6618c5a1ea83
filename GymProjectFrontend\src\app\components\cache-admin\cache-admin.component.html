<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Cache verileri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

    <!-- Page Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center gap-2">
                <h5 class="mb-0">
                  <i class="fas fa-database me-2"></i>
                  Cache Yönetimi
                </h5>
                <app-help-button
                  guideId="cache-admin"
                  position="inline"
                  size="small"
                  tooltip="Cache yönetimi hakkında yardım al">
                </app-help-button>
              </div>
              
              <!-- Header Controls -->
              <div class="d-flex align-items-center gap-3">
                <!-- Auto Refresh Toggle -->
                <div class="form-check form-switch">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="autoRefreshSwitch"
                    [(ngModel)]="autoRefreshEnabled"
                    (change)="toggleAutoRefresh()">
                  <label class="form-check-label" for="autoRefreshSwitch">
                    <small>Otomatik Yenileme ({{refreshInterval}}s)</small>
                  </label>
                </div>
                
                <!-- Manual Refresh Button -->
                <button 
                  class="btn btn-outline-primary btn-sm"
                  (click)="refreshData()"
                  [disabled]="isStatisticsLoading || isHealthLoading || isKeysLoading">
                  <i class="fas fa-sync-alt" [class.fa-spin]="isStatisticsLoading || isHealthLoading || isKeysLoading"></i>
                  Yenile
                </button>
              </div>
            </div>
            <p class="text-muted mb-0 mt-1">
              Redis cache monitoring ve yönetim paneli
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body p-0">
            <nav class="nav nav-pills nav-fill cache-nav-tabs">
              <button 
                class="nav-link"
                [class.active]="activeTab === 'overview'"
                (click)="setActiveTab('overview')">
                <i class="fas fa-chart-line me-2"></i>
                Genel Bakış
              </button>
              <button 
                class="nav-link"
                [class.active]="activeTab === 'keys'"
                (click)="setActiveTab('keys')">
                <i class="fas fa-key me-2"></i>
                Cache Key'leri
              </button>
              <button 
                class="nav-link"
                [class.active]="activeTab === 'operations'"
                (click)="setActiveTab('operations')">
                <i class="fas fa-cogs me-2"></i>
                İşlemler
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview'" class="tab-content">
      
      <!-- Health Status Cards -->
      <div class="row mb-4">
        <!-- Connection Status -->
        <div class="col-md-3">
          <div class="card cache-status-card">
            <div class="card-body text-center">
              <div class="cache-status-icon" [ngClass]="getHealthStatusClass()">
                <i [class]="getHealthStatusIcon()"></i>
              </div>
              <h6 class="card-title mt-2">Bağlantı Durumu</h6>
              <p class="card-text" [ngClass]="getHealthStatusClass()">
                {{ health?.isConnected ? 'Bağlı' : 'Bağlantı Yok' }}
              </p>
              <small class="text-muted" *ngIf="health?.responseTime">
                Yanıt Süresi: {{ formatDuration(health.responseTime) }}
              </small>
            </div>
          </div>
        </div>

        <!-- Performance Status -->
        <div class="col-md-3">
          <div class="card cache-status-card">
            <div class="card-body text-center">
              <div class="cache-status-icon" [ngClass]="getPerformanceClass()">
                <i class="fas fa-tachometer-alt"></i>
              </div>
              <h6 class="card-title mt-2">Performans</h6>
              <p class="card-text" [ngClass]="getPerformanceClass()">
                {{ health?.pingTime ? formatDuration(health.pingTime) : 'N/A' }}
              </p>
              <small class="text-muted">Ping Süresi</small>
            </div>
          </div>
        </div>

        <!-- Total Keys -->
        <div class="col-md-3">
          <div class="card cache-status-card">
            <div class="card-body text-center">
              <div class="cache-status-icon text-info">
                <i class="fas fa-database"></i>
              </div>
              <h6 class="card-title mt-2">Toplam Key</h6>
              <p class="card-text text-info">
                {{ statistics?.totalKeys || 0 | number }}
              </p>
              <small class="text-muted">Aktif Cache</small>
            </div>
          </div>
        </div>

        <!-- Memory Usage -->
        <div class="col-md-3">
          <div class="card cache-status-card">
            <div class="card-body text-center">
              <div class="cache-status-icon text-warning">
                <i class="fas fa-memory"></i>
              </div>
              <h6 class="card-title mt-2">Bellek Kullanımı</h6>
              <p class="card-text text-warning">
                {{ statistics?.totalMemoryUsageMB || 0 | number:'1.2-2' }} MB
              </p>
              <small class="text-muted">Cache Boyutu</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Statistics -->
      <div class="row">
        <!-- Entity Breakdown -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="fas fa-chart-pie me-2"></i>
                Entity Bazlı Dağılım
              </h6>
            </div>
            <div class="card-body">
              <div *ngIf="isStatisticsLoading" class="text-center py-4">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <p class="mt-2 mb-0">Yükleniyor...</p>
              </div>
              
              <div *ngIf="!isStatisticsLoading && getEntityKeys().length > 0" class="entity-breakdown">
                <div 
                  *ngFor="let item of getEntityKeys()" 
                  class="entity-item d-flex justify-content-between align-items-center mb-2">
                  <div class="entity-info">
                    <span class="entity-name">{{ getEntityLabel(item.entity) }}</span>
                    <small class="text-muted d-block">{{ item.entity }}</small>
                  </div>
                  <div class="entity-count">
                    <span class="badge bg-primary">{{ item.count | number }}</span>
                  </div>
                </div>
              </div>
              
              <div *ngIf="!isStatisticsLoading && getEntityKeys().length === 0" class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p class="mb-0">Henüz cache verisi yok</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Server Information -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="fas fa-server me-2"></i>
                Redis Server Bilgileri
              </h6>
            </div>
            <div class="card-body">
              <div *ngIf="isHealthLoading" class="text-center py-4">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <p class="mt-2 mb-0">Yükleniyor...</p>
              </div>
              
              <div *ngIf="!isHealthLoading && health?.serverInfo" class="server-info">
                <div class="info-item d-flex justify-content-between mb-2">
                  <span class="info-label">Redis Versiyonu:</span>
                  <span class="info-value">{{ health.serverInfo.version || 'N/A' }}</span>
                </div>
                <div class="info-item d-flex justify-content-between mb-2">
                  <span class="info-label">Çalışma Süresi:</span>
                  <span class="info-value">{{ health.serverInfo.uptimeInSeconds || 'N/A' }}</span>
                </div>
                <div class="info-item d-flex justify-content-between mb-2">
                  <span class="info-label">Kullanılan Bellek:</span>
                  <span class="info-value">{{ health.serverInfo.usedMemoryHuman || 'N/A' }}</span>
                </div>
                <div class="info-item d-flex justify-content-between mb-2">
                  <span class="info-label">Maksimum Bellek:</span>
                  <span class="info-value">{{ health.serverInfo.maxMemoryHuman || 'N/A' }}</span>
                </div>
                
                <!-- Memory Usage Progress Bar -->
                <div class="mt-3" *ngIf="getMemoryUsagePercentage() > 0">
                  <label class="form-label small">Bellek Kullanım Oranı</label>
                  <div class="progress">
                    <div 
                      class="progress-bar" 
                      [ngClass]="getMemoryUsageClass()"
                      [style.width.%]="getMemoryUsagePercentage()">
                      {{ getMemoryUsagePercentage() }}%
                    </div>
                  </div>
                </div>
              </div>
              
              <div *ngIf="!isHealthLoading && !health?.serverInfo" class="text-center py-4 text-muted">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p class="mb-0">Server bilgileri alınamadı</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cache Keys Tab -->
    <div *ngIf="activeTab === 'keys'" class="tab-content">

      <!-- Search and Filter Controls -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row align-items-end">
                <div class="col-md-6">
                  <label class="form-label">Arama Pattern'i</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Cache key pattern'i girin..."
                      [(ngModel)]="searchPattern"
                      (input)="onSearchPatternChange()">
                  </div>
                  <small class="form-text text-muted">
                    Örnek: member:*, payment:list, *details
                  </small>
                </div>

                <div class="col-md-4">
                  <label class="form-label">Entity Filtresi</label>
                  <select
                    class="form-select"
                    [(ngModel)]="selectedEntity"
                    (change)="onEntityFilterChange()">
                    <option *ngFor="let entity of availableEntities" [value]="entity.key">
                      {{ entity.label }}
                    </option>
                  </select>
                </div>

                <div class="col-md-2">
                  <button
                    class="btn btn-outline-secondary w-100"
                    (click)="loadCacheKeys()"
                    [disabled]="isKeysLoading">
                    <i class="fas fa-sync-alt" [class.fa-spin]="isKeysLoading"></i>
                    Yenile
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Keys List -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Cache Key'leri
                <span class="badge bg-secondary ms-2" *ngIf="cacheKeys?.pagination">
                  {{ cacheKeys.pagination.totalCount | number }}
                </span>
              </h6>

              <div class="d-flex align-items-center gap-2">
                <small class="text-muted">
                  Sayfa başına: {{ pageSize }}
                </small>
                <select
                  class="form-select form-select-sm"
                  [(ngModel)]="pageSize"
                  (change)="onPageChange(1)"
                  style="width: auto;">
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>

            <div class="card-body p-0">
              <!-- Loading State -->
              <div *ngIf="isKeysLoading" class="text-center py-5">
                <div class="spinner-border" role="status"></div>
                <p class="mt-3 mb-0">Cache key'leri yükleniyor...</p>
              </div>

              <!-- Keys Table -->
              <div *ngIf="!isKeysLoading && cacheKeys?.keys && cacheKeys.keys.length > 0" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Cache Key</th>
                      <th>Tip</th>
                      <th>TTL</th>
                      <th>Boyut</th>
                      <th>Oluşturulma</th>
                      <th width="100">İşlemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let key of cacheKeys.keys">
                      <td>
                        <div class="cache-key-cell">
                          <code class="cache-key-text">{{ key.key }}</code>
                          <button
                            class="btn btn-link btn-sm p-0 ms-2"
                            (click)="copyToClipboard(key.key)"
                            title="Kopyala">
                            <i class="fas fa-copy"></i>
                          </button>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ key.type }}</span>
                      </td>
                      <td>
                        <span *ngIf="key.ttl && key.ttl > 0" class="text-success">
                          {{ formatTTL(key.ttl) }}
                        </span>
                        <span *ngIf="!key.ttl || key.ttl <= 0" class="text-muted">
                          Kalıcı
                        </span>
                      </td>
                      <td>
                        <span class="text-muted">{{ formatBytes(key.memoryUsage) }}</span>
                      </td>
                      <td>
                        <span class="text-muted" *ngIf="key.createdAt">
                          {{ key.createdAt | date:'short' }}
                        </span>
                        <span class="text-muted" *ngIf="!key.createdAt">N/A</span>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-info"
                            (click)="viewCacheKeyValue(key.key)"
                            title="Değeri Görüntüle">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button
                            class="btn btn-outline-danger"
                            (click)="deleteCacheKey(key.key)"
                            title="Sil">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Empty State -->
              <div *ngIf="!isKeysLoading && (!cacheKeys?.keys || cacheKeys.keys.length === 0)" class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">Cache key bulunamadı</h6>
                <p class="text-muted mb-0">
                  Arama kriterlerinizi değiştirmeyi deneyin
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Operations Tab -->
    <div *ngIf="activeTab === 'operations'" class="tab-content">

      <!-- Cache Operations -->
      <div class="row">
        <!-- Clear Operations -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="fas fa-trash me-2"></i>
                Cache Temizleme İşlemleri
              </h6>
            </div>
            <div class="card-body">
              <div class="d-grid gap-3">
                <!-- Clear Tenant Cache -->
                <div class="operation-item">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">Şirket Cache'ini Temizle</h6>
                      <p class="text-muted mb-0 small">
                        Şirketinize ait tüm cache verilerini siler
                      </p>
                    </div>
                    <span class="badge bg-warning">Dikkatli</span>
                  </div>
                  <button
                    class="btn btn-outline-warning w-100"
                    (click)="clearTenantCache()"
                    [disabled]="isOperationLoading">
                    <i class="fas fa-building me-2"></i>
                    Şirket Cache'ini Temizle
                  </button>
                </div>

                <!-- Clear by Pattern -->
                <div class="operation-item">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">Pattern ile Temizle</h6>
                      <p class="text-muted mb-0 small">
                        Belirli pattern'e uygun cache'leri siler
                      </p>
                    </div>
                    <span class="badge bg-info">Seçmeli</span>
                  </div>
                  <div class="input-group mb-2">
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Örn: member:*, payment:list"
                      [(ngModel)]="clearPattern">
                    <button
                      class="btn btn-outline-info"
                      (click)="clearCacheByPattern(clearPattern)"
                      [disabled]="isOperationLoading || !clearPattern">
                      <i class="fas fa-filter me-2"></i>
                      Temizle
                    </button>
                  </div>
                </div>

                <!-- Quick Clear Buttons -->
                <div class="operation-item">
                  <h6 class="mb-2">Hızlı Temizleme</h6>
                  <div class="d-flex flex-wrap gap-2">
                    <button
                      class="btn btn-outline-secondary btn-sm"
                      (click)="clearCacheByPattern('member:*')"
                      [disabled]="isOperationLoading">
                      <i class="fas fa-users me-1"></i>
                      Üyeler
                    </button>
                    <button
                      class="btn btn-outline-secondary btn-sm"
                      (click)="clearCacheByPattern('payment:*')"
                      [disabled]="isOperationLoading">
                      <i class="fas fa-credit-card me-1"></i>
                      Ödemeler
                    </button>
                    <button
                      class="btn btn-outline-secondary btn-sm"
                      (click)="clearCacheByPattern('membership:*')"
                      [disabled]="isOperationLoading">
                      <i class="fas fa-id-card me-1"></i>
                      Üyelikler
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Operations -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="fas fa-rocket me-2"></i>
                Performans İşlemleri
              </h6>
            </div>
            <div class="card-body">
              <div class="d-grid gap-3">
                <!-- Cache Warmup -->
                <div class="operation-item">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">Cache Warmup</h6>
                      <p class="text-muted mb-0 small">
                        Sık kullanılan verileri cache'e önceden yükler
                      </p>
                    </div>
                    <span class="badge bg-success">Önerilen</span>
                  </div>
                  <button
                    class="btn btn-outline-success w-100"
                    (click)="warmupCache()"
                    [disabled]="isOperationLoading">
                    <i class="fas fa-fire me-2"></i>
                    Cache Warmup Başlat
                  </button>
                </div>

                <!-- Connection Test -->
                <div class="operation-item">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">Bağlantı Testi</h6>
                      <p class="text-muted mb-0 small">
                        Redis bağlantısını test eder
                      </p>
                    </div>
                    <span class="badge bg-info">Test</span>
                  </div>
                  <button
                    class="btn btn-outline-info w-100"
                    (click)="testConnection()"
                    [disabled]="isOperationLoading">
                    <i class="fas fa-network-wired me-2"></i>
                    Bağlantıyı Test Et
                  </button>
                </div>

                <!-- Performance Metrics -->
                <div class="operation-item">
                  <h6 class="mb-2">Performans Metrikleri</h6>
                  <div class="row text-center">
                    <div class="col-6">
                      <div class="metric-card">
                        <div class="metric-value" [ngClass]="getPerformanceClass()">
                          {{ health?.pingTime ? formatDuration(health.pingTime) : 'N/A' }}
                        </div>
                        <div class="metric-label">Ping</div>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="metric-card">
                        <div class="metric-value text-info">
                          {{ statistics?.totalKeys || 0 | number }}
                        </div>
                        <div class="metric-label">Keys</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Operation Status -->
      <div class="row mt-4" *ngIf="isOperationLoading">
        <div class="col-12">
          <div class="card">
            <div class="card-body text-center py-4">
              <div class="spinner-border text-primary mb-3" role="status"></div>
              <h6 class="mb-2">İşlem Devam Ediyor</h6>
              <p class="text-muted mb-0">
                Cache işlemi gerçekleştiriliyor, lütfen bekleyin...
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
