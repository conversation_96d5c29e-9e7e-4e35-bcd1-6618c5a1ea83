/* Cache Admin Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation Tabs */
.cache-nav-tabs {
  border-bottom: none;
}

.cache-nav-tabs .nav-link {
  border: none;
  border-radius: 0;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 1rem 1.5rem;
  transition: all var(--transition-speed) var(--transition-timing);
  background: transparent;
  border-bottom: 3px solid transparent;
}

.cache-nav-tabs .nav-link:hover {
  background: var(--bg-secondary);
  color: var(--primary);
  border-bottom-color: var(--primary-light);
}

.cache-nav-tabs .nav-link.active {
  background: var(--primary-light);
  color: var(--primary);
  border-bottom-color: var(--primary);
  font-weight: 600;
}

/* Status Cards */
.cache-status-card {
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed) var(--transition-timing);
  height: 100%;
}

.cache-status-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.cache-status-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.cache-status-card .card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.cache-status-card .card-text {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

/* Entity Breakdown */
.entity-breakdown {
  max-height: 300px;
  overflow-y: auto;
}

.entity-item {
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  background: var(--bg-secondary);
  margin-bottom: 0.5rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.entity-item:hover {
  background: var(--primary-light);
  transform: translateX(5px);
}

.entity-name {
  font-weight: 600;
  color: var(--text-primary);
}

.entity-count .badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
}

/* Server Info */
.server-info {
  font-size: 0.9rem;
}

.info-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-value {
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
}

/* Cache Keys Table */
.cache-key-cell {
  display: flex;
  align-items: center;
  max-width: 300px;
}

.cache-key-text {
  font-size: 0.8rem;
  background: var(--bg-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
  margin-right: 0.5rem;
}

.table-responsive {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.table th {
  background: var(--bg-secondary);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--text-secondary);
  padding: 1rem 0.75rem;
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
  background: var(--bg-secondary);
}

/* Operations */
.operation-item {
  padding: 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background: var(--bg-primary);
  transition: all var(--transition-speed) var(--transition-timing);
}

.operation-item:hover {
  border-color: var(--primary-light);
  background: var(--primary-light);
}

.operation-item h6 {
  color: var(--text-primary);
  font-weight: 600;
}

.operation-item .badge {
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
}

/* Metric Cards */
.metric-card {
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Progress Bars */
.progress {
  height: 8px;
  border-radius: var(--border-radius-pill);
  background: var(--bg-tertiary);
}

.progress-bar {
  border-radius: var(--border-radius-pill);
  transition: width 0.6s ease;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius-md);
  font-weight: 500;
  transition: all var(--transition-speed) var(--transition-timing);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-outline-primary:hover {
  background: var(--primary);
  border-color: var(--primary);
}

.btn-outline-warning:hover {
  background: var(--warning);
  border-color: var(--warning);
  color: white;
}

.btn-outline-success:hover {
  background: var(--success);
  border-color: var(--success);
  color: white;
}

.btn-outline-info:hover {
  background: var(--info);
  border-color: var(--info);
  color: white;
}

.btn-outline-danger:hover {
  background: var(--danger);
  border-color: var(--danger);
  color: white;
}

/* Form Controls */
.form-control, .form-select {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) var(--transition-timing);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.input-group-text {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-md);
}

/* Loading States */
.spinner-border {
  color: var(--primary);
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .cache-status-card .card-text {
    font-size: 1.25rem;
  }
  
  .cache-status-icon {
    font-size: 2rem;
  }
  
  .operation-item {
    padding: 1rem;
  }
  
  .metric-value {
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  .cache-nav-tabs {
    flex-direction: column;
  }
  
  .cache-nav-tabs .nav-link {
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
  }
  
  .cache-nav-tabs .nav-link.active {
    border-bottom-color: var(--primary);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .cache-status-card {
  background: var(--bg-secondary);
}

[data-theme="dark"] .table th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .operation-item {
  background: var(--bg-secondary);
  border-color: var(--bg-tertiary);
}

[data-theme="dark"] .operation-item:hover {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .metric-card {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .cache-key-text {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}
