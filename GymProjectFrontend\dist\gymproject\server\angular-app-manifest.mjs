
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'ca31841f5b22808ebecc6691c9136386a7ab708d866d97c1c97bd41eaa465c2f', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'a554adf038ab53f317705a137bbe471ef4b9b6ca85ea9c2fe032dd3ba9be0e80', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-V3UH3NNJ.css': {size: 302582, hash: 'KVkHM28HPpc', text: () => import('./assets-chunks/styles-V3UH3NNJ_css.mjs').then(m => m.default)}
  },
};
